---
description: 
globs: 
alwaysApply: true
---
# 开发环境说明

本项目在Windows系统上开发，执行命令时使用cmd或PowerShell的相关命令，而不要使用各种shell的命令。

## 命令行工具说明

- 请使用Windows命令提示符(cmd)或PowerShell中的命令
- 不要使用Bash、Zsh等Unix/Linux shell命令
- 文件路径使用Windows风格的反斜杠(\)或双反斜杠(\\)

## 常用命令对照参考

| 功能     | Windows命令 (cmd/PowerShell)                            | 不要使用的Shell命令 |
| -------- | ------------------------------------------------------- | ------------------- |
| 目录列表 | `dir`                                                   | `ls`                |
| 切换目录 | `cd <目录>`                                             | `cd <目录>`         |
| 清屏     | `cls` (cmd) 或 `Clear-Host` (PowerShell)                | `clear`             |
| 复制文件 | `copy <源> <目标>` 或 `Copy-Item <源> <目标>`           | `cp <源> <目标>`    |
| 移动文件 | `move <源> <目标>` 或 `Move-Item <源> <目标>`           | `mv <源> <目标>`    |
| 删除文件 | `del <文件>` 或 `Remove-Item <文件>`                    | `rm <文件>`         |

| 创建目录 | `mkdir <目录>` 或 `New-Item -ItemType Directory <目录>` | `mkdir <目录>`      |