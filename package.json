{"name": "stop-food-frontend", "version": "1.0.0", "description": "基于uni-app开发的热量管理微信小程序", "main": "main.js", "scripts": {"serve": "npm run dev:mp-weixin", "build": "npm run build:mp-weixin", "dev:app": "uni-app", "dev:app-android": "uni-app --platform app-android", "dev:app-ios": "uni-app --platform app-ios", "dev:custom": "uni-app", "dev:h5": "uni-app --platform h5", "dev:mp-alipay": "uni-app --platform mp-alipay", "dev:mp-baidu": "uni-app --platform mp-baidu", "dev:mp-kuaishou": "uni-app --platform mp-kuaishou", "dev:mp-lark": "uni-app --platform mp-lark", "dev:mp-qq": "uni-app --platform mp-qq", "dev:mp-toutiao": "uni-app --platform mp-toutiao", "dev:mp-weixin": "uni-app --platform mp-weixin", "dev:quickapp-webview": "uni-app --platform quickapp-webview", "build:app": "uni-app build", "build:app-android": "uni-app build --platform app-android", "build:app-ios": "uni-app build --platform app-ios", "build:custom": "uni-app build", "build:h5": "uni-app build --platform h5", "build:mp-alipay": "uni-app build --platform mp-alipay", "build:mp-baidu": "uni-app build --platform mp-baidu", "build:mp-kuaishou": "uni-app build --platform mp-kuaishou", "build:mp-lark": "uni-app build --platform mp-lark", "build:mp-qq": "uni-app build --platform mp-qq", "build:mp-toutiao": "uni-app build --platform mp-toutiao", "build:mp-weixin": "uni-app build --platform mp-weixin", "build:quickapp-webview": "uni-app build --platform quickapp-webview"}, "keywords": ["uni-app", "微信小程序", "热量管理", "健康"], "author": "", "license": "MIT", "uni-app": {"scripts": {}}, "dependencies": {"@dcloudio/uni-app": "3.0.0-3081220230817001", "@dcloudio/uni-components": "3.0.0-3081220230817001", "@dcloudio/uni-h5": "3.0.0-3081220230817001", "@dcloudio/uni-mp-alipay": "3.0.0-3081220230817001", "@dcloudio/uni-mp-baidu": "3.0.0-3081220230817001", "@dcloudio/uni-mp-kuaishou": "3.0.0-3081220230817001", "@dcloudio/uni-mp-lark": "3.0.0-3081220230817001", "@dcloudio/uni-mp-qq": "3.0.0-3081220230817001", "@dcloudio/uni-mp-toutiao": "3.0.0-3081220230817001", "@dcloudio/uni-mp-weixin": "3.0.0-3081220230817001", "@dcloudio/uni-quickapp-webview": "3.0.0-3081220230817001", "vue": "^3.2.47"}, "devDependencies": {"@dcloudio/types": "^3.3.2", "@dcloudio/uni-automator": "3.0.0-3081220230817001", "@dcloudio/uni-cli-shared": "3.0.0-3081220230817001", "@dcloudio/vite-plugin-uni": "3.0.0-3081220230817001", "vite": "^4.0.0"}, "browserslist": ["Android >= 4.4", "ios >= 9"]}