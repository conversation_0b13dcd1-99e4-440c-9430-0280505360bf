# 热量管理小程序

基于uni-app框架开发的微信小程序，用于热量管理和健康记录。

## 项目信息

- **框架**: uni-app
- **平台**: 微信小程序
- **AppID**: wx36d336375b1c07b1
- **开发工具**: HBuilderX / 微信开发者工具

## 项目结构

```
stop_food_frontend/
├── pages/                      # 页面目录
│   ├── calorie-diary/          # 热量日记页面
│   │   └── calorie-diary.vue
│   ├── calorie-plan/           # 热量规划页面
│   │   └── calorie-plan.vue
│   └── profile/                # 个人中心页面
│       └── profile.vue
├── static/                     # 静态资源目录
│   └── images/                 # 图片资源
│       ├── tab-diary.svg       # 热量日记图标
│       ├── tab-diary-active.svg
│       ├── tab-plan.svg        # 热量规划图标
│       ├── tab-plan-active.svg
│       ├── tab-profile.svg     # 个人中心图标
│       ├── tab-profile-active.svg
│       └── README.md           # 图标说明
├── components/                 # 组件目录
├── App.vue                     # 应用入口文件
├── main.js                     # 主入口文件
├── manifest.json               # 应用配置文件
├── pages.json                  # 页面路由配置
├── uni.scss                    # 全局样式变量
└── package.json                # 项目依赖配置
```

## 页面功能

### 1. 热量日记页面 (calorie-diary)
- 显示今日热量剩余情况
- 热量统计和进度条
- 食物记录列表
- 体重和步数显示
- 拍照识别和语音输入功能

### 2. 热量规划页面 (calorie-plan)
- 每日热量计划设置
- 基础代谢信息显示
- 减重模式选择
- 体重预测图表

### 3. 个人中心页面 (profile)
- 用户信息展示
- 功能菜单列表
- 设置和帮助入口

## 底部导航栏

配置了三个Tab页面：
- 热量日记 (首页)
- 热量规划
- 个人中心

支持选中和未选中两种状态的图标切换。

## 开发说明

### 运行项目

1. 使用HBuilderX打开项目
2. 选择运行到微信小程序
3. 在微信开发者工具中预览

### 样式规范

- 主色调：#00D4AA (绿色)
- 背景色：#f5f5f5 (浅灰)
- 文字颜色：#333333 (深灰)
- 辅助文字：#999999 (中灰)

### 注意事项

1. 当前为静态页面实现，未包含接口调用
2. 图标使用SVG格式，需要根据实际设计图替换
3. 页面样式已按照设计图进行像素级还原
4. 项目配置了微信小程序AppID，可直接在微信开发者工具中运行

## 后续开发

- [ ] 接入后端API
- [ ] 实现数据存储
- [ ] 添加用户登录功能
- [ ] 完善图片上传和识别功能
- [ ] 优化页面交互体验
