<template>
	<view id="app">
		<!-- 这里是应用的根组件 -->
	</view>
</template>

<script>
	export default {
		onLaunch: function() {
			console.log('App Launch')
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		}
	}
</script>

<style>
	/*每个页面公共css */
	page {
		background-color: #f5f5f5;
	}

	/* 全局样式 - 微信小程序兼容版本 */
	view, text, image, button, input, textarea {
		box-sizing: border-box;
	}

	.container {
		padding: 0;
		margin: 0;
	}
</style>
