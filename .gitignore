# ===================================
# uni-app 微信小程序项目 .gitignore
# ===================================

# ===== uni-app 编译产物 =====
# uni-app 编译生成的目录
unpackage/
dist/
build/

# 微信小程序编译产物
.hbuilderx/
.vscode/launch.json

# 各平台编译产物
platforms/

# ===== Node.js 依赖包 =====
# 依赖包目录
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# 包管理器锁定文件（可选择性忽略）
# package-lock.json
# yarn.lock
# pnpm-lock.yaml

# ===== IDE 配置文件 =====
# HBuilderX 配置
.hbuilderx/
.project
.settings/

# VSCode 配置
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json

# WebStorm/IntelliJ IDEA
.idea/
*.iml
*.ipr
*.iws

# Sublime Text
*.sublime-project
*.sublime-workspace

# 微信开发者工具配置
project.config.json
project.private.config.json
sitemap.json

# ===== 系统文件 =====
# macOS 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows 系统文件
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/

# Linux 系统文件
*~
.fuse_hidden*
.directory
.Trash-*

# ===== 日志文件 =====
# 各种日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 运行时日志
pids/
*.pid
*.seed
*.pid.lock

# ===== 环境配置文件 =====
# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 本地配置文件
config.local.js
local.config.js

# ===== 缓存文件 =====
# npm 缓存
.npm
.eslintcache

# Yarn 缓存
.yarn-integrity

# 构建缓存
.cache/
.parcel-cache/

# ===== 临时文件 =====
# 临时目录
tmp/
temp/
.tmp/

# 编辑器临时文件
*.swp
*.swo
*~

# 压缩文件
*.zip
*.tar.gz
*.rar

# ===== 测试相关 =====
# 测试覆盖率报告
coverage/
*.lcov
.nyc_output/

# Jest 缓存
.jest/

# ===== 其他 =====
# 备份文件
*.bak
*.backup
*.old

# 文档生成
docs/

# 发布包
*.tgz

# 运行时数据
.runtime/

# 微信小程序云开发相关
cloudfunctions/

# ===== 项目特定文件 =====
# 图标生成工具（临时文件）
create-*.html
generate-*.html
*验证报告.md
*修复报告.md

# 设计稿切图目录（可选）
# static/images/*_slices/

# ===== 保留的重要文件 =====
# 以下文件应该被提交到仓库
# !manifest.json
# !pages.json
# !App.vue
# !main.js
# !uni.scss
# !package.json
# !README.md
