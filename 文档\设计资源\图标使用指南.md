# 图标使用指南

## 概述

本文档规范了项目中图标文件的管理、命名和使用方式，确保团队成员能够统一、高效地使用图标资源。

## 目录结构

### 图标文件存放位置

```
static/images/
├── customer-service.png          # 客服图标（标准分辨率）
├── <EMAIL>          # 提醒图标（高分辨率）
├── avatar.png                    # 头像图标
├── camera.png                    # 相机图标
├── voice.png                     # 语音图标
├── ...                          # 其他项目图标
└── 热量日记_slices/              # 原始切图资源（不直接使用）
    ├── icon-客服.png
    ├── icon-客服@2x.png
    ├── icon-开启提醒.png
    ├── icon-开启提醒@2x.png
    └── ...
```

### 文件管理原则

1. **统一存放**：所有项目使用的图标统一存放在 `static/images/` 根目录
2. **原始资源保留**：原始切图资源保留在子目录中作为备份，但不直接引用
3. **语义化命名**：使用英文语义化命名，便于理解和维护

## 命名规范

### 标准命名格式

```
功能描述-类型.png
功能描述-类型@2x.png  # 高分辨率版本
```

### 命名示例

| 功能 | 标准分辨率 | 高分辨率 | 说明 |
|------|------------|----------|------|
| 客服 | `customer-service.png` | `<EMAIL>` | 联系客服功能 |
| 提醒 | `reminder-icon.png` | `<EMAIL>` | 开启提醒功能 |
| 相机 | `camera.png` | `<EMAIL>` | 拍照功能 |
| 语音 | `voice.png` | `<EMAIL>` | 语音输入功能 |

### 命名规则

- 使用小写英文字母
- 单词间用连字符 `-` 分隔
- 高分辨率版本添加 `@2x` 后缀
- 文件名要具有描述性，能够清楚表达图标用途

## 使用方法

### 在Vue组件中引用图标

#### 基本语法

```vue
<template>
  <image 
    class="icon-class" 
    src="/static/images/图标文件名.png" 
    mode="aspectFit"
  ></image>
</template>
```

#### 实际使用示例

**1. 客服图标使用**

```vue
<!-- 联系客服 -->
<view class="header-left">
  <image 
    class="avatar" 
    src="/static/images/customer-service.png" 
    mode="aspectFit"
  ></image>
  <text class="greeting">联系客服</text>
</view>
```

**2. 提醒图标使用（高分辨率）**

```vue
<!-- 开启提醒按钮 -->
<view class="start-btn">
  <image 
    class="reminder-icon" 
    src="/static/images/<EMAIL>" 
    mode="aspectFit"
  ></image>
  <text class="btn-text">开启提醒</text>
</view>
```

### CSS样式配置

#### 图标尺寸设置

```css
/* 标准图标尺寸 */
.avatar {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  margin-right: 12rpx;
}

/* 按钮内图标 */
.reminder-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: 8rpx;
}

/* 小图标 */
.small-icon {
  width: 24rpx;
  height: 24rpx;
}
```

## 高分辨率图标（@2x）使用说明

### 什么时候使用@2x图标

1. **高DPI设备适配**：在高分辨率屏幕上显示更清晰
2. **重要功能图标**：如主要操作按钮的图标
3. **用户界面核心元素**：如导航图标、状态图标等

### @2x图标优势

- **清晰度**：在高分辨率设备上显示更加清晰
- **细节保留**：保持图标的细节和锐度
- **用户体验**：提供更好的视觉体验

### 使用注意事项

```vue
<!-- 推荐：使用高分辨率版本 -->
<image src="/static/images/<EMAIL>" mode="aspectFit"></image>

<!-- 不推荐：直接使用原始切图路径 -->
<image src="/static/images/热量日记_slices/icon-开启提醒@2x.png"></image>
```

## 图标文件管理最佳实践

### 1. 文件添加流程

当需要添加新图标时，按以下步骤操作：

1. **从切图资源复制**
   ```bash
   copy "static\images\热量日记_slices\icon-新图标@2x.png" "static\images\<EMAIL>"
   ```

2. **使用语义化命名**
   - 根据功能命名，如 `search-icon.png`、`settings-icon.png`
   - 优先使用@2x高分辨率版本

3. **更新代码引用**
   ```vue
   <image src="/static/images/<EMAIL>" mode="aspectFit"></image>
   ```

### 2. 文件维护原则

- **定期清理**：删除不再使用的图标文件
- **版本管理**：保持@2x和标准版本的同步
- **文档更新**：新增图标时及时更新本文档

### 3. 团队协作规范

- **统一路径**：所有团队成员使用相同的图标路径
- **命名一致**：遵循统一的命名规范
- **代码审查**：确保新增图标符合规范

## 常见问题解决

### Q: 图标显示模糊怎么办？
A: 使用@2x高分辨率版本，并确保CSS尺寸设置合适。

### Q: 如何选择图标尺寸？
A: 根据使用场景选择：
- 头像/主要图标：40rpx
- 按钮内图标：36rpx
- 辅助图标：24rpx

### Q: 可以直接使用切图目录中的文件吗？
A: 不推荐。应该复制到根目录并重命名后使用，便于管理和维护。

## 项目实际应用示例

### 热量日记页面头部

```vue
<template>
  <view class="custom-header">
    <view class="header-content">
      <!-- 联系客服 -->
      <view class="header-left">
        <image 
          class="avatar" 
          src="/static/images/customer-service.png" 
          mode="aspectFit"
        ></image>
        <text class="greeting">联系客服</text>
      </view>
      
      <!-- 页面标题 -->
      <view class="header-center">
        <text class="title">热量日记</text>
      </view>
    </view>

    <!-- 开启提醒按钮 -->
    <view class="record-tip">
      <text class="tip-text">3秒记录+定制方案+精准控卡</text>
      <view class="start-btn">
        <image 
          class="reminder-icon" 
          src="/static/images/<EMAIL>" 
          mode="aspectFit"
        ></image>
        <text class="btn-text">开启提醒</text>
      </view>
    </view>
  </view>
</template>
```

---

**文档版本**：v1.0  
**最后更新**：2025-07-31  
**维护人员**：开发团队
